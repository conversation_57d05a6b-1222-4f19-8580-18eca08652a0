import { Timestamp } from 'firebase/firestore';

export interface FirestoreUser {
  id: string;
  email: string;
  username: string;
  nickname?: string;
  photoURL?: string;
  createdAt: Timestamp | string | Date;
  lastLogin?: Timestamp | string | Date;
  bannedAt?: Timestamp | string | Date;
  suspendedAt?: Timestamp | string | Date;
  suspendedUntil?: Timestamp | string | Date;
  status: 'active' | 'banned' | 'suspended';
  role: 'admin' | 'moderator' | 'user';
  banned?: boolean;
  banReason?: string;
  suspendReason?: string;
  lastIpAddress?: string;
  emailVerified?: boolean;
  emailVerifiedAt?: Timestamp | string | Date;
  emailVerifiedBy?: string;
  ipHistory?: Array<{
    ip: string;
    timestamp: Timestamp | string | Date;
    userAgent?: string;
  }>;
}

export interface User extends Omit<FirestoreUser, 'createdAt' | 'lastLogin' | 'bannedAt' | 'suspendedAt' | 'suspendedUntil' | 'emailVerifiedAt' | 'ipHistory'> {
  createdAt: Date | null;
  lastLogin?: Date | null;
  bannedAt?: Date | null;
  suspendedAt?: Date | null;
  suspendedUntil?: Date | null;
  emailVerifiedAt?: Date | null;
  ipHistory?: Array<{
    ip: string;
    timestamp: Date;
    userAgent?: string;
  }>;
}