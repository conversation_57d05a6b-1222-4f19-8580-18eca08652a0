import { db, auth } from "@/lib/firebase";
import { doc, getDoc, updateDoc, deleteDoc, collection, getDocs } from "firebase/firestore";
import { sendPasswordResetEmail } from "firebase/auth";
import { User } from "@/lib/types/user";

export const getUserById = async (userId: string) => {
  try {
    const userDoc = await getDoc(doc(db, "users", userId));
    if (!userDoc.exists()) return null;

    const data = userDoc.data();
    return {
      id: userDoc.id,
      ...data,
      createdAt: data.createdAt || "",
      lastLogin: data.lastLogin?.toDate?.() || data.lastLogin,
      bannedAt: data.bannedAt || ""
    };
  } catch (error) {
    console.error("Error getting user:", error);
    return null;
  }
};

export async function banUser(userId: string): Promise<void> {
  try {
    await updateDoc(doc(db, "users", userId), {
      banned: true,
      bannedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error banning user:', error);
    throw error;
  }
}

export async function unbanUser(userId: string): Promise<void> {
  try {
    await updateDoc(doc(db, "users", userId), {
      banned: false,
      bannedAt: null
    });
  } catch (error) {
    console.error('Error unbanning user:', error);
    throw error;
  }
}

export async function resetPassword(email: string): Promise<void> {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error resetting password:', error);
    throw error;
  }
}

export async function suspendUser(userId: string, reason: string, duration?: number): Promise<void> {
  try {
    const suspendedUntil = duration ? new Date(Date.now() + duration * 24 * 60 * 60 * 1000) : null;

    await updateDoc(doc(db, "users", userId), {
      status: 'suspended',
      suspendedAt: new Date().toISOString(),
      suspendReason: reason,
      suspendedUntil: suspendedUntil?.toISOString() || null
    });
  } catch (error) {
    console.error('Error suspending user:', error);
    throw error;
  }
}

export async function unsuspendUser(userId: string): Promise<void> {
  try {
    await updateDoc(doc(db, "users", userId), {
      status: 'active',
      suspendedAt: null,
      suspendReason: null,
      suspendedUntil: null
    });
  } catch (error) {
    console.error('Error unsuspending user:', error);
    throw error;
  }
}

export async function markEmailAsVerified(userId: string): Promise<void> {
  try {
    await updateDoc(doc(db, "users", userId), {
      emailVerified: true,
      emailVerifiedAt: new Date().toISOString(),
      emailVerifiedBy: 'admin'
    });
  } catch (error) {
    console.error('Error marking email as verified:', error);
    throw error;
  }
}

export async function resendVerificationEmail(email: string): Promise<void> {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error resending verification email:', error);
    throw error;
  }
}

export async function getUserGroupMemberships(userId: string) {
  try {
    const groupsRef = collection(db, "groups");
    const groupsSnapshot = await getDocs(groupsRef);

    const userGroups = [];

    for (const groupDoc of groupsSnapshot.docs) {
      const groupData = groupDoc.data();
      const members = groupData.members || [];

      // Kullanıcının bu grupta olup olmadığını kontrol et
      const memberInfo = members.find((member: any) => member.userId === userId);

      if (memberInfo) {
        userGroups.push({
          id: groupDoc.id,
          name: groupData.name,
          description: groupData.description,
          category: groupData.category,
          platform: groupData.platform,
          type: groupData.type,
          role: memberInfo.role || 'member',
          joinedAt: memberInfo.joinedAt?.toDate() || null,
          isActive: groupData.isActive
        });
      }
    }

    return userGroups;
  } catch (error) {
    console.error('Error getting user group memberships:', error);
    throw error;
  }
}

export async function deleteUser(userId: string): Promise<void> {
  try {
    await deleteDoc(doc(db, "users", userId));
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
}