"use client";

import { useState } from "react";
import { useAuth } from "@/lib/hooks/useAuth";
import { useGroupActions } from "@/lib/hooks/useGroupActions";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Plus, Loader2 } from "lucide-react";
import Image from "next/image";

const categories = [
  { id: "fps", label: "FPS" },
  { id: "moba", label: "MOBA" },
  { id: "mmorpg", label: "MMORPG" },
  { id: "battle-royale", label: "Battle Royale" },
  { id: "strategy", label: "Strateji" },
];

const platforms = [
  { id: "pc", label: "PC" },
  { id: "playstation", label: "PlayStation" },
  { id: "xbox", label: "Xbox" },
  { id: "mobile", label: "Mobil" },
];

export function CreateGroupDialog() {
  const { user } = useAuth();
  const { createGroup, loading } = useGroupActions();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!user) return;

    const formData = new FormData(event.currentTarget);
    const groupData = {
      name: formData.get("name") as string,
      description: formData.get("description") as string,
      category: formData.get("category") as string,
      platform: formData.get("platform") as string,
      type: formData.get("type") as "public" | "private",
      tags: (formData.get("tags") as string).split(",").map(tag => tag.trim()),
      admins: [user.uid],
      members: [user.uid],
      pendingRequests: [],
      image: `/images/categories/${formData.get("category")}.webp`,
      ownerId: user.uid,
      isActive: true,
      updatedAt: new Date()
    };

    try {
      await createGroup(groupData, user.uid);
      toast({
        title: "Grup oluşturuldu",
        description: "Grubunuz başarıyla oluşturuldu!",
      });
      setOpen(false);
    } catch (error) {
      toast({
        title: "Hata",
        description: "Grup oluşturulurken bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Yeni Grup Oluştur
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Yeni Grup Oluştur</DialogTitle>
          <DialogDescription>
            Yeni bir oyun grubu oluştur ve oyuncuları davet et.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="mt-4">
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Grup Adı</Label>
              <Input
                id="name"
                name="name"
                placeholder="Grubunuzun adı"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Açıklama</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Grup hakkında kısa bir açıklama"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Kategori</Label>
              <Select name="category" required>
                <SelectTrigger>
                  <SelectValue placeholder="Seç..." />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="platform">Platform</Label>
              <Select name="platform" required>
                <SelectTrigger>
                  <SelectValue placeholder="Seç..." />
                </SelectTrigger>
                <SelectContent>
                  {platforms.map((platform) => (
                    <SelectItem key={platform.id} value={platform.id}>
                      {platform.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">Grup Tipi</Label>
              <Select name="type" required>
                <SelectTrigger>
                  <SelectValue placeholder="Seç..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">Herkese Açık</SelectItem>
                  <SelectItem value="private">Özel (Onay Gerekli)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="tags">Etiketler</Label>
              <Input
                id="tags"
                name="tags"
                placeholder="fps, competitive, ranked (virgülle ayırın)"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Oluşturuluyor...
                </>
              ) : (
                "Oluştur"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}