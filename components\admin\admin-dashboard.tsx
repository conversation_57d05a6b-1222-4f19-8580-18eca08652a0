"use client";

import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Users, MessageSquare, UserPlus, Shield, AlertTriangle } from "lucide-react";
import { adminService } from "@/lib/services/admin";
import { AdminChart } from "./admin-chart";
import { AdminMetricCard } from "./admin-metric-card";
import { AdminAlertsList } from "./admin-alerts-list";
import { useAuth } from "@/lib/hooks/useAuth";
import { useSuperAdmin } from "@/lib/hooks/useSuperAdmin";
import { useAdminCommands } from '@/lib/hooks/useAdminCommands';
import { useAdminLogs } from '@/lib/hooks/useAdminLogs';
import { WithPermission } from './with-permission';

export function AdminDashboard() {
  const { user } = useAuth();
  const { adminData } = useSuperAdmin(user?.uid);
  const { executeCommand, loading: commandLoading } = useAdminCommands(user?.uid || '');
  const { getLogs } = useAdminLogs();
  const { data: metrics } = useQuery({
    queryKey: ['admin-metrics'],
    queryFn: () => adminService.getMetrics(),
  });

  // Son aktiviteleri getir
  const { data: recentLogs } = useQuery({
    queryKey: ['admin-logs'],
    queryFn: () => getLogs({ 
      adminId: user?.uid,
      limit: 5
    }),
    enabled: !!user?.uid
  });

  // Komut işleyicileri
  const handleBanUser = async (userId: string, reason: string) => {
    await executeCommand('banUser', userId, reason);
  };

  const handleCloseGroup = async (groupId: string, reason: string) => {
    await executeCommand('closeGroup', groupId, reason);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Yönetim Paneli</h1>
          <p className="text-muted-foreground mt-1">
            Hoş geldin, {adminData?.fullName || user?.email}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <AdminMetricCard
          title="Toplam Kullanıcı"
          value={metrics?.totalUsers || 0}
          icon={Users}
          description="Kayıtlı kullanıcı sayısı"
          permission="users.view"
        />
        <AdminMetricCard
          title="Aktif Kullanıcı"
          value={metrics?.activeUsers || 0}
          icon={UserPlus}
          description="Son 30 gün içinde aktif"
          permission="users.view"
        />
        <AdminMetricCard
          title="Toplam Grup"
          value={metrics?.totalGroups || 0}
          icon={MessageSquare}
          description="Oluşturulan grup sayısı"
          permission="groups.view"
        />
        <AdminMetricCard
          title="Bekleyen Rapor"
          value={metrics?.pendingReports || 0}
          icon={Shield}
          description="İncelenmesi gereken raporlar"
          variant={(metrics?.pendingReports || 0) > 0 ? "destructive" : "default"}
          permission={["reports.view", "reports.manage"]}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Kullanıcı Aktivitesi</h2>
          <AdminChart />
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <h2 className="text-lg font-semibold">Son Uyarılar</h2>
          </div>
          <AdminAlertsList />
        </Card>
      </div>

      <WithPermission permission="users.manage">
        <Card className="p-6">
          <h2 className="text-lg font-semibold">Kullanıcı Yönetimi</h2>
          {/* Sadece users.manage yetkisi olanlar görebilir */}
        </Card>
      </WithPermission>

      <WithPermission 
        permission={['groups.manage', 'groups.view']} 
        requireAll={false}
      >
        <Card className="p-6">
          <h2 className="text-lg font-semibold">Grup Yönetimi</h2>
          {/* groups.manage VEYA groups.view yetkisi olanlar görebilir */}
        </Card>
      </WithPermission>
    </div>
  );
} 