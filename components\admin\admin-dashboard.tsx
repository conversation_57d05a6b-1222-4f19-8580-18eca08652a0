"use client";

import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, MessageSquare, UserPlus, Shield, AlertTriangle, Settings, BarChart3, <PERSON>r<PERSON>heck, Plus } from "lucide-react";
import { adminService } from "@/lib/services/admin";
import { AdminChart } from "./admin-chart";
import { AdminMetricCard } from "./admin-metric-card";
import { AdminAlertsList } from "./admin-alerts-list";
import { useAuth } from "@/lib/hooks/useAuth";
import { useSuperAdmin } from "@/lib/hooks/useSuperAdmin";
import { useAdminCommands } from '@/lib/hooks/useAdminCommands';
import { useAdminLogs } from '@/lib/hooks/useAdminLogs';
import { WithPermission } from './with-permission';
import Link from "next/link";
import { useRouter } from "next/navigation";

export function AdminDashboard() {
  const { user } = useAuth();
  const { adminData } = useSuperAdmin(user?.uid);
  const { executeCommand, loading: commandLoading } = useAdminCommands(user?.uid || '');
  const { getLogs } = useAdminLogs();
  const router = useRouter();
  const { data: metrics } = useQuery({
    queryKey: ['admin-metrics'],
    queryFn: () => adminService.getMetrics(),
  });

  // Son aktiviteleri getir
  const { data: recentLogs } = useQuery({
    queryKey: ['admin-logs'],
    queryFn: () => getLogs({ 
      adminId: user?.uid,
      limit: 5
    }),
    enabled: !!user?.uid
  });

  // Komut işleyicileri
  const handleBanUser = async (userId: string, reason: string) => {
    await executeCommand('banUser', userId, reason);
  };

  const handleCloseGroup = async (groupId: string, reason: string) => {
    await executeCommand('closeGroup', groupId, reason);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Yönetim Paneli</h1>
          <p className="text-muted-foreground mt-1">
            Hoş geldin, {adminData?.fullName || user?.email}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <AdminMetricCard
          title="Toplam Kullanıcı"
          value={metrics?.totalUsers || 0}
          icon={Users}
          description="Kayıtlı kullanıcı sayısı"
          permission="users.view"
        />
        <AdminMetricCard
          title="Aktif Kullanıcı"
          value={metrics?.activeUsers || 0}
          icon={UserPlus}
          description="Son 30 gün içinde aktif"
          permission="users.view"
        />
        <AdminMetricCard
          title="Toplam Grup"
          value={metrics?.totalGroups || 0}
          icon={MessageSquare}
          description="Oluşturulan grup sayısı"
          permission="groups.view"
        />
        <AdminMetricCard
          title="Bekleyen Rapor"
          value={metrics?.pendingReports || 0}
          icon={Shield}
          description="İncelenmesi gereken raporlar"
          variant={(metrics?.pendingReports || 0) > 0 ? "destructive" : "default"}
          permission={["reports.view", "reports.manage"]}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Kullanıcı Aktivitesi</h2>
          <AdminChart />
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <h2 className="text-lg font-semibold">Son Uyarılar</h2>
          </div>
          <AdminAlertsList />
        </Card>
      </div>

      {/* Quick Actions Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <WithPermission permission={["users.manage", "users.view"]} requireAll={false}>
          <Card className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <Users className="h-6 w-6 text-primary" />
              <h2 className="text-lg font-semibold">Kullanıcı Yönetimi</h2>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Kullanıcıları görüntüle, düzenle ve yönet
            </p>
            <div className="flex gap-2">
              <Button asChild size="sm">
                <Link href="/admin/users">
                  <Users className="h-4 w-4 mr-2" />
                  Kullanıcıları Görüntüle
                </Link>
              </Button>
              <WithPermission permission="users.manage">
                <Button asChild variant="outline" size="sm">
                  <Link href="/admin/users?action=add">
                    <Plus className="h-4 w-4 mr-2" />
                    Yeni Kullanıcı
                  </Link>
                </Button>
              </WithPermission>
            </div>
          </Card>
        </WithPermission>

        <WithPermission permission={["groups.manage", "groups.view"]} requireAll={false}>
          <Card className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <MessageSquare className="h-6 w-6 text-primary" />
              <h2 className="text-lg font-semibold">Grup Yönetimi</h2>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Grupları görüntüle, düzenle ve yönet
            </p>
            <div className="flex gap-2">
              <Button asChild size="sm">
                <Link href="/admin/groups">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Grupları Görüntüle
                </Link>
              </Button>
              <WithPermission permission="groups.manage">
                <Button asChild variant="outline" size="sm">
                  <Link href="/admin/groups?action=create">
                    <Plus className="h-4 w-4 mr-2" />
                    Yeni Grup
                  </Link>
                </Button>
              </WithPermission>
            </div>
          </Card>
        </WithPermission>

        <WithPermission permission={["reports.manage", "reports.view"]} requireAll={false}>
          <Card className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <Shield className="h-6 w-6 text-primary" />
              <h2 className="text-lg font-semibold">Moderasyon</h2>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Raporları incele ve moderasyon işlemleri yap
            </p>
            <div className="flex gap-2">
              <Button asChild size="sm">
                <Link href="/admin/moderation">
                  <Shield className="h-4 w-4 mr-2" />
                  Raporları İncele
                </Link>
              </Button>
              <WithPermission permission="reports.manage">
                <Button asChild variant="outline" size="sm">
                  <Link href="/admin/moderators">
                    <UserCheck className="h-4 w-4 mr-2" />
                    Moderatörler
                  </Link>
                </Button>
              </WithPermission>
            </div>
          </Card>
        </WithPermission>

        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <BarChart3 className="h-6 w-6 text-primary" />
            <h2 className="text-lg font-semibold">Analitik</h2>
          </div>
          <p className="text-sm text-muted-foreground mb-4">
            Platform istatistiklerini ve raporları görüntüle
          </p>
          <Button asChild size="sm">
            <Link href="/admin/reports">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analitik Görüntüle
            </Link>
          </Button>
        </Card>

        <WithPermission permission="settings.manage">
          <Card className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <Settings className="h-6 w-6 text-primary" />
              <h2 className="text-lg font-semibold">Sistem Ayarları</h2>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Platform ayarlarını yapılandır
            </p>
            <Button asChild size="sm">
              <Link href="/admin/settings">
                <Settings className="h-4 w-4 mr-2" />
                Ayarları Yönet
              </Link>
            </Button>
          </Card>
        </WithPermission>

        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <AlertTriangle className="h-6 w-6 text-primary" />
            <h2 className="text-lg font-semibold">Sistem Uyarıları</h2>
          </div>
          <p className="text-sm text-muted-foreground mb-4">
            Sistem uyarılarını ve bildirimleri yönet
          </p>
          <Button asChild size="sm">
            <Link href="/admin/alerts">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Uyarıları Görüntüle
            </Link>
          </Button>
        </Card>
      </div>
    </div>
  );
} 