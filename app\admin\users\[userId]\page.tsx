'use client';

import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { getUserById } from '@/lib/firebase/users';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ban, UserX, KeyRound, Trash2 } from "lucide-react";
import { banUser, unbanUser, resetPassword, deleteUser } from '@/lib/firebase/users';
import { FirestoreUser, User } from '../../../types/user';
import Loading from '@/app/loading';
import { useToast } from "@/hooks/use-toast";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";

export default function UserDetailsPage() {
  const params = useParams();
  const userId = typeof params?.userId === 'string' ? params.userId : '';
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isBanned, setIsBanned] = useState(false);
  const { success, error } = useToast();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const userData = await getUserById(userId as string) as FirestoreUser;
        if (!userData) {
          setUser(null);
          return;
        }

        const processedData: User = {
          ...userData,
          email: userData.email || '',
          username: userData.username || '',
          createdAt: parseFirebaseDate(userData.createdAt),
          lastLogin: parseFirebaseDate(userData.lastLogin),
          bannedAt: parseFirebaseDate(userData.bannedAt),
          status: userData.status || 'active',
          role: userData.role || 'user'
        };
        
        setUser(processedData);
        setIsBanned(processedData.banned || false);
      } catch (error) {
        console.error('Kullanıcı bilgileri yüklenirken hata:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) {
    return <Loading />;
  }

  if (!user) {
    return <div className="p-4">Kullanıcı bulunamadı.</div>;
  }

  const handleBanToggle = async () => {
    try {
      if (isBanned) {
        await unbanUser(user.id);
        success('Kullanıcının yasağı kaldırıldı');
      } else {
        await banUser(user.id);
        success('Kullanıcı başarıyla yasaklandı');
      }
      setIsBanned(!isBanned);
    } catch (err) {
      console.error('İşlem sırasında hata:', err);
      error('Bir hata oluştu');
    }
  };

  const handleResetPassword = async () => {
    try {
      await resetPassword(user.email);
      success('Şifre sıfırlama bağlantısı gönderildi');
    } catch (err) {
      console.error('Şifre sıfırlanırken hata:', err);
      error('Şifre sıfırlama işlemi başarısız oldu');
    }
  };

  const handleDeleteUser = async () => {
    try {
      await deleteUser(user.id);
      success('Kullanıcı başarıyla silindi');
    } catch (err) {
      console.error('Kullanıcı silinirken hata:', err);
      error('Kullanıcı silme işlemi başarısız oldu');
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Kullanıcı Detayları</h1>
      </div>
      
      <Card className="p-6">
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div>
            <p className="font-semibold">Kullanıcı ID:</p>
            <p className="text-muted-foreground">{user.id}</p>
          </div>
          <div>
            <p className="font-semibold">Email:</p>
            <p className="text-muted-foreground">{user.email}</p>
          </div>
          <div>
            <p className="font-semibold">Kullanıcı Adı:</p>
            <p className="text-muted-foreground">{user.username || '-'}</p>
          </div>
          <div>
            <p className="font-semibold">Kayıt Tarihi:</p>
            <p className="text-muted-foreground">
              {user.createdAt ? new Date(user.createdAt).toLocaleDateString('tr-TR') : '-'}
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Admin İşlemleri</h2>
          <div className="flex flex-wrap gap-4">
            <Button onClick={handleBanToggle} variant="destructive">
              <Ban className="w-4 h-4 mr-2" />
              {isBanned ? 'Yasağı Kaldır' : 'Kullanıcıyı Yasakla'}
            </Button>
            
            <Button onClick={handleResetPassword} variant="outline">
              <KeyRound className="w-4 h-4 mr-2" />
              Şifre Sıfırla
            </Button>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Kullanıcıyı Sil
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Kullanıcıyı Sil</AlertDialogTitle>
                  <AlertDialogDescription>
                    Bu kullanıcıyı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>İptal</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDeleteUser}>
                    Sil
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </Card>
    </div>
  );
} 

// Timestamp veya ISO string formatındaki tarihleri Date objesine çevirir
const parseFirebaseDate = (date: any): Date | null => {
  if (!date) return null;
  
  // Eğer Timestamp objesi ise
  if (date.seconds) {
    return new Date(date.seconds * 1000);
  }
  
  // Eğer ISO string ise
  if (typeof date === 'string') {
    return new Date(date);
  }
  
  return null;
}; 