'use client';

import { use<PERSON>arams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { getUserById } from '@/lib/firebase/users';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ban, UserX, KeyRound, Trash2, Clock, Edit, Save, X, Mail, CheckCircle, Users } from "lucide-react";
import { banUser, unbanUser, resetPassword, deleteUser, suspendUser, unsuspendUser, markEmailAsVerified, resendVerificationEmail, getUserGroupMemberships } from '@/lib/firebase/users';
import { FirestoreUser, User } from '../../../types/user';
import Loading from '@/app/loading';
import { useToast } from "@/hooks/use-toast";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { updateDoc, doc } from "firebase/firestore";
import { db } from "@/lib/firebase";

export default function UserDetailsPage() {
  const params = useParams();
  const userId = typeof params?.userId === 'string' ? params.userId : '';
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isBanned, setIsBanned] = useState(false);
  const [isSuspended, setIsSuspended] = useState(false);
  const [suspendReason, setSuspendReason] = useState("");
  const [suspendDuration, setSuspendDuration] = useState<string>("");
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    username: '',
    nickname: '',
    email: '',
    role: 'user' as 'admin' | 'moderator' | 'user'
  });
  const [userGroups, setUserGroups] = useState<any[]>([]);
  const [groupsLoading, setGroupsLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const userData = await getUserById(userId as string) as FirestoreUser;
        if (!userData) {
          setUser(null);
          return;
        }

        const processedData: User = {
          ...userData,
          email: userData.email || '',
          username: userData.username || '',
          createdAt: parseFirebaseDate(userData.createdAt),
          lastLogin: parseFirebaseDate(userData.lastLogin),
          bannedAt: parseFirebaseDate(userData.bannedAt),
          suspendedAt: parseFirebaseDate(userData.suspendedAt),
          suspendedUntil: parseFirebaseDate(userData.suspendedUntil),
          emailVerifiedAt: parseFirebaseDate(userData.emailVerifiedAt),
          status: userData.status || 'active',
          role: userData.role || 'user',
          ipHistory: userData.ipHistory?.map(entry => ({
            ...entry,
            timestamp: parseFirebaseDate(entry.timestamp) || new Date()
          })).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        };

        setUser(processedData);
        setIsBanned(processedData.banned || false);
        setIsSuspended(processedData.status === 'suspended');

        // Edit form'u doldur
        setEditForm({
          username: processedData.username || '',
          nickname: processedData.nickname || '',
          email: processedData.email || '',
          role: processedData.role || 'user'
        });
      } catch (error) {
        console.error('Kullanıcı bilgileri yüklenirken hata:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  // User groups'u fetch et
  useEffect(() => {
    const fetchUserGroups = async () => {
      if (!user?.id) return;

      setGroupsLoading(true);
      try {
        const groups = await getUserGroupMemberships(user.id);
        setUserGroups(groups);
      } catch (error) {
        console.error('Kullanıcı grupları yüklenirken hata:', error);
        toast({ title: "Hata", description: "Kullanıcı grupları yüklenemedi", variant: "destructive" });
      } finally {
        setGroupsLoading(false);
      }
    };

    fetchUserGroups();
  }, [user?.id, toast]);

  if (loading) {
    return <Loading />;
  }

  if (!user) {
    return <div className="p-4">Kullanıcı bulunamadı.</div>;
  }

  const handleBanToggle = async () => {
    try {
      if (isBanned) {
        await unbanUser(user.id);
        toast({ title: "Başarılı", description: "Kullanıcının yasağı kaldırıldı" });
      } else {
        await banUser(user.id);
        toast({ title: "Başarılı", description: "Kullanıcı başarıyla yasaklandı" });
      }
      setIsBanned(!isBanned);
    } catch (err) {
      console.error('İşlem sırasında hata:', err);
      toast({ title: "Hata", description: "Bir hata oluştu", variant: "destructive" });
    }
  };

  const handleSuspendToggle = async () => {
    try {
      if (isSuspended) {
        await unsuspendUser(user.id);
        toast({ title: "Başarılı", description: "Kullanıcının askısı kaldırıldı" });
        setIsSuspended(false);
      } else {
        const duration = suspendDuration ? parseInt(suspendDuration) : undefined;
        await suspendUser(user.id, suspendReason, duration);
        toast({ title: "Başarılı", description: "Kullanıcı askıya alındı" });
        setIsSuspended(true);
      }
      setSuspendReason("");
      setSuspendDuration("");
    } catch (err) {
      console.error('İşlem sırasında hata:', err);
      toast({ title: "Hata", description: "Bir hata oluştu", variant: "destructive" });
    }
  };

  const handleResetPassword = async () => {
    try {
      await resetPassword(user.email);
      toast({ title: "Başarılı", description: "Şifre sıfırlama bağlantısı gönderildi" });
    } catch (err) {
      console.error('Şifre sıfırlanırken hata:', err);
      toast({ title: "Hata", description: "Şifre sıfırlama işlemi başarısız oldu", variant: "destructive" });
    }
  };

  const handleDeleteUser = async () => {
    try {
      await deleteUser(user.id);
      toast({ title: "Başarılı", description: "Kullanıcı başarıyla silindi" });
    } catch (err) {
      console.error('Kullanıcı silinirken hata:', err);
      toast({ title: "Hata", description: "Kullanıcı silme işlemi başarısız oldu", variant: "destructive" });
    }
  };

  const handleSaveEdit = async () => {
    try {
      await updateDoc(doc(db, "users", user.id), {
        username: editForm.username,
        nickname: editForm.nickname,
        email: editForm.email,
        role: editForm.role,
        updatedAt: new Date()
      });

      // User state'ini güncelle
      setUser(prev => prev ? {
        ...prev,
        username: editForm.username,
        nickname: editForm.nickname,
        email: editForm.email,
        role: editForm.role
      } : null);

      setIsEditing(false);
      toast({ title: "Başarılı", description: "Kullanıcı bilgileri güncellendi" });
    } catch (err) {
      console.error('Kullanıcı güncellenirken hata:', err);
      toast({ title: "Hata", description: "Kullanıcı güncelleme işlemi başarısız oldu", variant: "destructive" });
    }
  };

  const handleCancelEdit = () => {
    setEditForm({
      username: user?.username || '',
      nickname: user?.nickname || '',
      email: user?.email || '',
      role: user?.role || 'user'
    });
    setIsEditing(false);
  };

  const handleMarkEmailVerified = async () => {
    try {
      await markEmailAsVerified(user.id);
      setUser(prev => prev ? { ...prev, emailVerified: true, emailVerifiedAt: new Date() } : null);
      toast({ title: "Başarılı", description: "Email doğrulandı olarak işaretlendi" });
    } catch (err) {
      console.error('Email doğrulama işlemi sırasında hata:', err);
      toast({ title: "Hata", description: "Email doğrulama işlemi başarısız oldu", variant: "destructive" });
    }
  };

  const handleResendVerificationEmail = async () => {
    try {
      await resendVerificationEmail(user.email);
      toast({ title: "Başarılı", description: "Doğrulama emaili gönderildi" });
    } catch (err) {
      console.error('Email gönderme sırasında hata:', err);
      toast({ title: "Hata", description: "Email gönderme işlemi başarısız oldu", variant: "destructive" });
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Kullanıcı Detayları</h1>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button onClick={handleSaveEdit} size="sm">
                <Save className="w-4 h-4 mr-2" />
                Kaydet
              </Button>
              <Button onClick={handleCancelEdit} variant="outline" size="sm">
                <X className="w-4 h-4 mr-2" />
                İptal
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)} variant="outline" size="sm">
              <Edit className="w-4 h-4 mr-2" />
              Düzenle
            </Button>
          )}
        </div>
      </div>
      
      <Card className="p-6">
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div>
            <p className="font-semibold">Kullanıcı ID:</p>
            <p className="text-muted-foreground">{user.id}</p>
          </div>
          <div>
            <p className="font-semibold">Email:</p>
            {isEditing ? (
              <Input
                value={editForm.email}
                onChange={(e) => setEditForm(prev => ({ ...prev, email: e.target.value }))}
                className="mt-1"
              />
            ) : (
              <p className="text-muted-foreground">{user.email}</p>
            )}
          </div>
          <div>
            <p className="font-semibold">Kullanıcı Adı:</p>
            {isEditing ? (
              <Input
                value={editForm.username}
                onChange={(e) => setEditForm(prev => ({ ...prev, username: e.target.value }))}
                className="mt-1"
              />
            ) : (
              <p className="text-muted-foreground">{user.username || '-'}</p>
            )}
          </div>
          <div>
            <p className="font-semibold">Takma Ad:</p>
            {isEditing ? (
              <Input
                value={editForm.nickname}
                onChange={(e) => setEditForm(prev => ({ ...prev, nickname: e.target.value }))}
                className="mt-1"
                placeholder="Takma ad"
              />
            ) : (
              <p className="text-muted-foreground">{user.nickname || '-'}</p>
            )}
          </div>
          <div>
            <p className="font-semibold">Rol:</p>
            {isEditing ? (
              <Select value={editForm.role} onValueChange={(value: 'admin' | 'moderator' | 'user') => setEditForm(prev => ({ ...prev, role: value }))}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">Kullanıcı</SelectItem>
                  <SelectItem value="moderator">Moderatör</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <p className="text-muted-foreground">{user.role}</p>
            )}
          </div>
          <div>
            <p className="font-semibold">Durum:</p>
            <p className="text-muted-foreground">
              {user.banned ? 'Yasaklı' : user.status === 'suspended' ? 'Askıda' : 'Aktif'}
            </p>
          </div>
          <div>
            <p className="font-semibold">Kayıt Tarihi:</p>
            <p className="text-muted-foreground">
              {user.createdAt ? new Date(user.createdAt).toLocaleDateString('tr-TR') : '-'}
            </p>
          </div>
          <div>
            <p className="font-semibold">Son Giriş:</p>
            <p className="text-muted-foreground">
              {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('tr-TR') : '-'}
            </p>
          </div>
          <div>
            <p className="font-semibold">Son IP Adresi:</p>
            <p className="text-muted-foreground font-mono">
              {user.lastIpAddress || '-'}
            </p>
          </div>
          <div>
            <p className="font-semibold">Email Doğrulandı:</p>
            <div className="flex items-center gap-2">
              <p className="text-muted-foreground">
                {user.emailVerified ? 'Evet' : 'Hayır'}
              </p>
              {user.emailVerified && (
                <CheckCircle className="w-4 h-4 text-green-500" />
              )}
            </div>
          </div>
          {user.emailVerifiedAt && (
            <div>
              <p className="font-semibold">Email Doğrulama Tarihi:</p>
              <p className="text-muted-foreground">
                {new Date(user.emailVerifiedAt).toLocaleDateString('tr-TR')}
              </p>
            </div>
          )}
          {user.banned && user.banReason && (
            <div>
              <p className="font-semibold">Yasaklama Sebebi:</p>
              <p className="text-muted-foreground">{user.banReason}</p>
            </div>
          )}
          {user.status === 'suspended' && user.suspendReason && (
            <div>
              <p className="font-semibold">Askıya Alma Sebebi:</p>
              <p className="text-muted-foreground">{user.suspendReason}</p>
            </div>
          )}
          {user.suspendedUntil && (
            <div>
              <p className="font-semibold">Askı Bitiş Tarihi:</p>
              <p className="text-muted-foreground">
                {new Date(user.suspendedUntil).toLocaleDateString('tr-TR')}
              </p>
            </div>
          )}
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Admin İşlemleri</h2>
          <div className="flex flex-wrap gap-4">
            <Button onClick={handleBanToggle} variant="destructive">
              <Ban className="w-4 h-4 mr-2" />
              {isBanned ? 'Yasağı Kaldır' : 'Kullanıcıyı Yasakla'}
            </Button>

            {isSuspended ? (
              <Button onClick={handleSuspendToggle} variant="outline">
                <Clock className="w-4 h-4 mr-2" />
                Askıyı Kaldır
              </Button>
            ) : (
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="secondary">
                    <Clock className="w-4 h-4 mr-2" />
                    Askıya Al
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Kullanıcıyı Askıya Al</DialogTitle>
                    <DialogDescription>
                      Kullanıcıyı askıya almak için sebep ve süre belirtin.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="suspend-reason">Askıya Alma Sebebi</Label>
                      <Textarea
                        id="suspend-reason"
                        placeholder="Askıya alma sebebini yazın..."
                        value={suspendReason}
                        onChange={(e) => setSuspendReason(e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="suspend-duration">Süre (Gün)</Label>
                      <Select value={suspendDuration} onValueChange={setSuspendDuration}>
                        <SelectTrigger>
                          <SelectValue placeholder="Süre seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">Süresiz</SelectItem>
                          <SelectItem value="1">1 Gün</SelectItem>
                          <SelectItem value="3">3 Gün</SelectItem>
                          <SelectItem value="7">1 Hafta</SelectItem>
                          <SelectItem value="14">2 Hafta</SelectItem>
                          <SelectItem value="30">1 Ay</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => {
                      setSuspendReason("");
                      setSuspendDuration("");
                    }}>
                      İptal
                    </Button>
                    <Button
                      onClick={handleSuspendToggle}
                      disabled={!suspendReason.trim()}
                    >
                      Askıya Al
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}

            <Button onClick={handleResetPassword} variant="outline">
              <KeyRound className="w-4 h-4 mr-2" />
              Şifre Sıfırla
            </Button>

            {!user.emailVerified && (
              <Button onClick={handleMarkEmailVerified} variant="outline">
                <CheckCircle className="w-4 h-4 mr-2" />
                Email'i Doğrula
              </Button>
            )}

            <Button onClick={handleResendVerificationEmail} variant="outline">
              <Mail className="w-4 h-4 mr-2" />
              Doğrulama Email'i Gönder
            </Button>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Kullanıcıyı Sil
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Kullanıcıyı Sil</AlertDialogTitle>
                  <AlertDialogDescription>
                    Bu kullanıcıyı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>İptal</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDeleteUser}>
                    Sil
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </Card>

      {/* IP History Section */}
      {user.ipHistory && user.ipHistory.length > 0 && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">IP Geçmişi</h2>
          <div className="space-y-2">
            {user.ipHistory.slice(0, 10).map((entry, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-4">
                  <span className="font-mono text-sm">{entry.ip}</span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(entry.timestamp).toLocaleString('tr-TR')}
                  </span>
                </div>
                {entry.userAgent && (
                  <span className="text-xs text-muted-foreground max-w-md truncate">
                    {entry.userAgent}
                  </span>
                )}
              </div>
            ))}
            {user.ipHistory.length > 10 && (
              <p className="text-sm text-muted-foreground text-center mt-4">
                Ve {user.ipHistory.length - 10} kayıt daha...
              </p>
            )}
          </div>
        </Card>
      )}

      {/* User Groups Section */}
      <Card className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Users className="h-5 w-5" />
          <h2 className="text-xl font-semibold">Grup Üyelikleri</h2>
        </div>

        {groupsLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : userGroups.length === 0 ? (
          <p className="text-muted-foreground text-center py-8">
            Bu kullanıcı henüz hiçbir gruba üye değil.
          </p>
        ) : (
          <div className="space-y-3">
            {userGroups.map((group) => (
              <div key={group.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <h3 className="font-medium">{group.name}</h3>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      group.role === 'admin' ? 'bg-red-100 text-red-800' :
                      group.role === 'moderator' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {group.role === 'admin' ? 'Admin' :
                       group.role === 'moderator' ? 'Moderatör' : 'Üye'}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      group.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {group.isActive ? 'Aktif' : 'Pasif'}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {group.description || 'Açıklama yok'}
                  </p>
                  <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                    <span>Kategori: {group.category}</span>
                    <span>Platform: {group.platform}</span>
                    <span>Tip: {group.type === 'public' ? 'Açık' : 'Kapalı'}</span>
                    {group.joinedAt && (
                      <span>Katılım: {new Date(group.joinedAt).toLocaleDateString('tr-TR')}</span>
                    )}
                  </div>
                </div>
                <Button asChild variant="outline" size="sm">
                  <a href={`/admin/groups/${group.id}`} target="_blank" rel="noopener noreferrer">
                    Grubu Görüntüle
                  </a>
                </Button>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
} 

// Timestamp veya ISO string formatındaki tarihleri Date objesine çevirir
const parseFirebaseDate = (date: any): Date | null => {
  if (!date) return null;
  
  // Eğer Timestamp objesi ise
  if (date.seconds) {
    return new Date(date.seconds * 1000);
  }
  
  // Eğer ISO string ise
  if (typeof date === 'string') {
    return new Date(date);
  }
  
  return null;
}; 