'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Lock, Users, Calendar, Shield } from "lucide-react";
import { toggleGroupStatus } from "@/lib/firebase/group";
import { doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { toast } from 'sonner';
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";
import { getUserById } from '@/lib/firebase/users';
import Image from 'next/image';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";

interface GroupMember {
  id: string;
  username: string;
  email: string;
  photoURL?: string;
  role?: string;
}

interface GroupData {
  id: string;
  name: string;
  description: string;
  members: string[];
  admins: string[];
  memberCount: number;
  category: string;
  platform: string;
  type: 'public' | 'private';
  isActive: boolean;
  createdAt: Date;
}

export default function GroupDetailPage() {
  const { groupId } = useParams() as { groupId: string };
  const [group, setGroup] = useState<GroupData | null>(null);
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGroupAndMembers = async () => {
      try {
        // Grup bilgilerini al
        const groupDoc = await getDoc(doc(db, 'groups', groupId));
        if (!groupDoc.exists()) {
          toast.error('Grup bulunamadı');
          return;
        }

        const groupData: GroupData = {
          id: groupDoc.id,
          name: groupDoc.data().name || '',
          description: groupDoc.data().description || '',
          members: groupDoc.data().members || [],
          admins: groupDoc.data().admins || [],
          memberCount: groupDoc.data().memberCount || 0,
          category: groupDoc.data().category || '',
          platform: groupDoc.data().platform || '',
          type: groupDoc.data().type || 'public',
          isActive: groupDoc.data().isActive ?? true,
          createdAt: groupDoc.data().createdAt?.toDate() || new Date()
        };
        setGroup(groupData);

        // Üye bilgilerini al
        const memberData = await Promise.all(
          groupData.members.map((memberId: string) => getUserById(memberId))
        );
        setMembers(memberData.filter(Boolean) as unknown as GroupMember[]);
      } catch (error) {
        console.error('Veri yüklenirken hata:', error);
        toast.error('Veriler yüklenirken bir hata oluştu');
      } finally {
        setLoading(false);
      }
    };

    fetchGroupAndMembers();
  }, [groupId]);

  const handleToggleStatus = async () => {
    if (!group) return;
    
    try {
      await toggleGroupStatus(groupId, !group.isActive);
      setGroup(prev => prev ? { ...prev, isActive: !prev.isActive } as GroupData : null);
    } catch (error) {
      console.error('Grup durumu güncellenirken hata:', error);
      toast.error('Grup durumu güncellenirken bir hata oluştu');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!group) return <div>Grup bulunamadı.</div>;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{group.name}</h1>
          <p className="text-muted-foreground mt-1">{group.description}</p>
        </div>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button 
              variant={group.isActive ? "destructive" : "default"}
            >
              <Lock className="w-4 h-4 mr-2" />
              {group.isActive ? 'Grubu Kapat' : 'Grubu Aç'}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {group.isActive ? 'Grubu Kapat' : 'Grubu Aç'}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {group.isActive 
                  ? 'Bu grubu kapatmak istediğinizden emin misiniz?' 
                  : 'Bu grubu açmak istediğinizden emin misiniz?'
                }
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>İptal</AlertDialogCancel>
              <AlertDialogAction onClick={handleToggleStatus}>
                {group.isActive ? 'Kapat' : 'Aç'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Grup Bilgileri</h2>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-muted-foreground" />
              <span>{group.memberCount} üye</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-muted-foreground" />
              <span>
                {formatDistanceToNow(group.createdAt, { addSuffix: true, locale: tr })} oluşturuldu
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary">{group.category}</Badge>
              <Badge variant="outline">{group.platform}</Badge>
              <Badge variant={group.type === 'public' ? 'default' : 'secondary'}>
                {group.type === 'public' ? 'Açık' : 'Özel'}
              </Badge>
            </div>
          </div>
        </Card>

        <Card className="md:col-span-2 p-6">
          <h2 className="text-lg font-semibold mb-4">Üyeler</h2>
          <div className="space-y-4">
            {members.map(member => (
              <div key={member.id} className="flex items-center justify-between p-2 hover:bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                    {member.photoURL ? (
                      <Image src={member.photoURL} alt={member.username} className="w-full h-full rounded-full" /> 
                    ) : (
                      <span className="text-lg">{member.username[0]}</span>
                    )}
                  </div>
                  <div>
                    <p className="font-medium">{member.username}</p>
                    <p className="text-sm text-muted-foreground">{member.email}</p>
                  </div>
                </div>
                {group.admins?.includes(member.id) && (
                  <Badge variant="secondary">
                    <Shield className="w-3 h-3 mr-1" />
                    Admin
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}